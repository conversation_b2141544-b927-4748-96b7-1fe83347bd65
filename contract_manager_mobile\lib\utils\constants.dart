class Constants {
  // API Configuration
  static const String baseUrl =
      'http://********:8000/api'; // Android emulator URL

  // For Android emulator, use: http://********:8000/api
  // For iOS simulator, use: http://localhost:8000/api
  // For physical device, use your computer's IP: http://192.168.1.xxx:8000/api

  // App Configuration
  static const String appName = 'Contract Manager';
  static const String appVersion = '1.0.0';

  // Storage Keys
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String themeKey = 'theme_mode';

  // API Endpoints
  static const String loginEndpoint = '/login';
  static const String registerEndpoint = '/register';
  static const String logoutEndpoint = '/logout';
  static const String profileEndpoint = '/profile';
  static const String forgotPasswordEndpoint = '/forgot-password';
  static const String resetPasswordEndpoint = '/reset-password';
  static const String googleAuthEndpoint = '/auth/google/callback';
  static const String facebookAuthEndpoint = '/auth/facebook/callback';

  // Contract Endpoints
  static const String contractsEndpoint = '/contracts';
  static const String signedContractsEndpoint = '/signed-contracts';
  static const String unsignedContractsEndpoint = '/unsigned-contracts';
  static const String partnersEndpoint = '/partners';

  // File Endpoints
  static const String uploadEndpoint = '/upload';
  static const String filesEndpoint = '/files';
  static const String downloadEndpoint = '/files/download';

  // Admin Endpoints
  static const String adminDashboardEndpoint = '/admin/dashboard-stats';
  static const String adminUsersEndpoint = '/admin/users';
  static const String adminPartnersEndpoint = '/admin/partners';
  static const String adminContractsEndpoint = '/admin/contracts';

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // File Upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedFileTypes = [
    'pdf',
    'doc',
    'docx',
    'jpg',
    'jpeg',
    'png',
  ];

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 8.0;
  static const double cardElevation = 2.0;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // Network Timeouts
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);

  // Error Messages
  static const String networkErrorMessage =
      'Network error occurred. Please check your connection.';
  static const String serverErrorMessage =
      'Server error occurred. Please try again later.';
  static const String unknownErrorMessage = 'An unexpected error occurred.';
  static const String authErrorMessage =
      'Authentication failed. Please login again.';
  static const String permissionErrorMessage =
      'You don\'t have permission to perform this action.';

  // Success Messages
  static const String loginSuccessMessage = 'Login successful!';
  static const String registerSuccessMessage = 'Registration successful!';
  static const String logoutSuccessMessage = 'Logout successful!';
  static const String updateSuccessMessage = 'Update successful!';
  static const String deleteSuccessMessage = 'Delete successful!';
  static const String uploadSuccessMessage = 'Upload successful!';

  // Validation
  static const int minPasswordLength = 8;
  static const int maxNameLength = 50;
  static const int maxEmailLength = 100;
  static const int maxPhoneLength = 20;

  // Regular Expressions
  static const String emailRegex =
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phoneRegex = r'^\+?[1-9]\d{1,14}$';
  static const String passwordRegex =
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$';
}
